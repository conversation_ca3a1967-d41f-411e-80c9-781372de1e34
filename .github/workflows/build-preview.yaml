name: Build Preview Deployment

on:
  pull_request:
    types: [opened, synchronize]
  workflow_dispatch:

jobs:
  build-preview:
    if: ${{ github.repository == 'jackyzha0/quartz' }}
    runs-on: ubuntu-latest
    name: Build Preview
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - run: npm ci

      - name: Check types and style
        run: npm run check

      - name: Build Quartz
        run: npx quartz build -d docs -v

      - name: Upload build artifact
        uses: actions/upload-artifact@v4
        with:
          name: preview-build
          path: public
