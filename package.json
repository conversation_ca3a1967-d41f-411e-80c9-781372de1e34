{"name": "@jackyzha0/quartz", "description": "🌱 publish your digital garden and notes as a website", "private": true, "version": "4.5.1", "type": "module", "author": "jackyzha0 <<EMAIL>>", "license": "MIT", "homepage": "https://quartz.jzhao.xyz", "repository": {"type": "git", "url": "https://github.com/jackyzha0/quartz.git"}, "scripts": {"quartz": "./quartz/bootstrap-cli.mjs", "docs": "npx quartz build --serve -d docs", "check": "tsc --noEmit && npx prettier . --check", "format": "npx prettier . --write", "test": "tsx --test", "profile": "0x -D prof ./quartz/bootstrap-cli.mjs build --concurrency=1"}, "engines": {"npm": ">=10.9.2", "node": ">=22"}, "keywords": ["site generator", "ssg", "digital-garden", "markdown", "blog", "quartz"], "bin": {"quartz": "./quartz/bootstrap-cli.mjs"}, "dependencies": {"@clack/prompts": "^0.11.0", "@floating-ui/dom": "^1.7.2", "@myriaddreamin/rehype-typst": "^0.6.0", "@napi-rs/simple-git": "0.1.21", "@tweenjs/tween.js": "^25.0.0", "ansi-truncate": "^1.2.0", "async-mutex": "^0.5.0", "chokidar": "^4.0.3", "cli-spinner": "^0.2.10", "d3": "^7.9.0", "esbuild-sass-plugin": "^3.3.1", "flexsearch": "0.7.43", "github-slugger": "^2.0.0", "globby": "^14.1.0", "gray-matter": "^4.0.3", "hast-util-to-html": "^9.0.5", "hast-util-to-jsx-runtime": "^2.3.6", "hast-util-to-string": "^3.0.1", "is-absolute-url": "^4.0.1", "js-yaml": "^4.1.0", "lightningcss": "^1.30.1", "mdast-util-find-and-replace": "^3.0.2", "mdast-util-to-hast": "^13.2.0", "mdast-util-to-string": "^4.0.0", "micromorph": "^0.4.5", "minimatch": "^10.0.3", "pixi.js": "^8.11.0", "preact": "^10.27.0", "preact-render-to-string": "^6.5.13", "pretty-bytes": "^7.0.0", "pretty-time": "^1.1.0", "reading-time": "^1.5.0", "rehype-autolink-headings": "^7.1.0", "rehype-citation": "^2.3.1", "rehype-katex": "^7.0.1", "rehype-mathjax": "^7.1.0", "rehype-pretty-code": "^0.14.1", "rehype-raw": "^7.0.0", "rehype-slug": "^6.0.0", "remark": "^15.0.1", "remark-breaks": "^4.0.0", "remark-frontmatter": "^5.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.2", "remark-smartypants": "^3.0.2", "rfdc": "^1.4.1", "satori": "^0.16.1", "serve-handler": "^6.1.6", "sharp": "^0.34.3", "shiki": "^1.26.2", "source-map-support": "^0.5.21", "to-vfile": "^8.0.0", "toml": "^3.0.0", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "vfile": "^6.0.3", "workerpool": "^9.3.3", "ws": "^8.18.3", "yargs": "^18.0.0"}, "devDependencies": {"@types/d3": "^7.4.3", "@types/hast": "^3.0.4", "@types/js-yaml": "^4.0.9", "@types/node": "^24.1.0", "@types/pretty-time": "^1.1.5", "@types/source-map-support": "^0.5.10", "@types/ws": "^8.18.1", "@types/yargs": "^17.0.33", "esbuild": "^0.25.8", "prettier": "^3.6.2", "tsx": "^4.20.3", "typescript": "^5.8.3"}}