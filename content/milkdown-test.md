# Milkdown Editor Test

Welcome to the **Milkdown Editor** integration test page!

## Features

This page demonstrates the Milkdown WYSIWYG markdown editor integration with Quartz. You should see an edit button above that allows you to:

- Toggle between view and edit modes
- Edit content in a rich WYSIWYG interface
- Save changes (currently saves to local state)
- Use keyboard shortcuts (Ctrl+E to toggle, Ctrl+S to save)

## Sample Content

Here's some sample markdown content to test with:

### Lists

- Item 1
- Item 2
- Item 3

### Code

```javascript
function hello() {
  console.log("Hello, Milkdown!");
}
```

### Blockquote

> This is a blockquote that demonstrates how Milkdown handles different markdown elements.

### Table

| Feature | Status |
|---------|--------|
| WYSIWYG Editing | ✅ |
| Markdown Support | ✅ |
| Quartz Integration | ✅ |

## Instructions

1. Click the "Edit" button to enter edit mode
2. Make some changes to the content
3. Click "Save" to save your changes
4. Toggle back to view mode to see the rendered result

*Try editing this content to test the Milkdown integration!*
