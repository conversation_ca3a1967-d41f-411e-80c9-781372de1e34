/*! MIT License
 * Copyright (c) 2018 GitHub Inc.
 * https://github.com/primer/primitives/blob/main/LICENSE
 */

main {
  --color-prettylights-syntax-comment: #6e7781;
  --color-prettylights-syntax-constant: #0550ae;
  --color-prettylights-syntax-entity: #8250df;
  --color-prettylights-syntax-storage-modifier-import: #24292f;
  --color-prettylights-syntax-entity-tag: #116329;
  --color-prettylights-syntax-keyword: #cf222e;
  --color-prettylights-syntax-string: #0a3069;
  --color-prettylights-syntax-variable: #953800;
  --color-prettylights-syntax-brackethighlighter-unmatched: #82071e;
  --color-prettylights-syntax-invalid-illegal-text: #f6f8fa;
  --color-prettylights-syntax-invalid-illegal-bg: #82071e;
  --color-prettylights-syntax-carriage-return-text: #f6f8fa;
  --color-prettylights-syntax-carriage-return-bg: #cf222e;
  --color-prettylights-syntax-string-regexp: #116329;
  --color-prettylights-syntax-markup-list: #3b2300;
  --color-prettylights-syntax-markup-heading: #0550ae;
  --color-prettylights-syntax-markup-italic: #24292f;
  --color-prettylights-syntax-markup-bold: #24292f;
  --color-prettylights-syntax-markup-deleted-text: #82071e;
  --color-prettylights-syntax-markup-deleted-bg: #ffebe9;
  --color-prettylights-syntax-markup-inserted-text: #116329;
  --color-prettylights-syntax-markup-inserted-bg: #dafbe1;
  --color-prettylights-syntax-markup-changed-text: #953800;
  --color-prettylights-syntax-markup-changed-bg: #ffd8b5;
  --color-prettylights-syntax-markup-ignored-text: #eaeef2;
  --color-prettylights-syntax-markup-ignored-bg: #0550ae;
  --color-prettylights-syntax-meta-diff-range: #8250df;
  --color-prettylights-syntax-brackethighlighter-angle: #57606a;
  --color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f;
  --color-prettylights-syntax-constant-other-reference-link: #0a3069;
  --color-btn-text: #4e4e4e; /* --darkgray */
  --color-btn-bg: #faf8f8; /* --light */
  --color-btn-border: rgb(43, 43, 43 / 15%); /* --dark */
  --color-btn-shadow: 0 1px 0 rgb(31 35 40 / 4%);
  --color-btn-inset-shadow: inset 0 1px 0 rgb(*********** / 25%);
  --color-btn-hover-bg: #f3f4f6;
  --color-btn-hover-border: rgb(43, 43, 43 / 15%); /* --dark */
  --color-btn-active-bg: hsl(220deg 14% 93% / 100%);
  --color-btn-active-border: rgb(31 35 40 / 15%);
  --color-btn-selected-bg: hsl(220deg 14% 94% / 100%);
  --color-btn-primary-text: #fff;
  --color-btn-primary-bg: #84a59d; /* --tertiary */
  --color-btn-primary-border: rgb(43, 43, 43 / 15%); /* --dark */
  --color-btn-primary-shadow: 0 1px 0 rgb(31 35 40 / 10%);
  --color-btn-primary-inset-shadow: inset 0 1px 0 rgb(*********** / 3%);
  --color-btn-primary-hover-bg: #284b63; /* --secondary */
  --color-btn-primary-hover-border: rgb(43, 43, 43 / 15%); /* --dark */
  --color-btn-primary-selected-bg: #284b63; /* --secondary */
  --color-btn-primary-selected-shadow: inset 0 1px 0 rgb(0 45 17 / 20%);
  --color-btn-primary-disabled-text: rgb(*********** / 80%);
  --color-btn-primary-disabled-bg: #94d3a2;
  --color-btn-primary-disabled-border: rgb(31 35 40 / 15%);
  --color-action-list-item-default-hover-bg: rgb(208 215 222 / 32%);
  --color-segmented-control-bg: #eaeef2;
  --color-segmented-control-button-bg: #fff;
  --color-segmented-control-button-selected-border: #8c959f;
  --color-fg-default: #2b2b2b; /* --dark */
  --color-fg-muted: #4e4e4e; /* --darkgray */
  --color-fg-subtle: #4e4e4e; /* --darkgray */
  --color-canvas-default: #fff;
  --color-canvas-overlay: #fff;
  --color-canvas-inset: #f6f8fa;
  --color-canvas-subtle: #f6f8fa;
  --color-border-default: #d0d7de;
  --color-border-muted: hsl(210deg 18% 87% / 100%);
  --color-neutral-muted: rgb(175 184 193 / 20%);
  --color-accent-fg: #0969da;
  --color-accent-emphasis: #0969da;
  --color-accent-muted: rgb(84 174 255 / 40%);
  --color-accent-subtle: #ddf4ff;
  --color-success-fg: #1a7f37;
  --color-attention-fg: #9a6700;
  --color-attention-muted: rgb(212 167 44 / 40%);
  --color-attention-subtle: #fff8c5;
  --color-danger-fg: #d1242f;
  --color-danger-muted: rgb(255 129 130 / 40%);
  --color-danger-subtle: #ffebe9;
  --color-primer-shadow-inset: inset 0 1px 0 rgb(208 215 222 / 20%);
  --color-scale-gray-1: #eaeef2;
  --color-scale-blue-1: #b6e3ff;

  /*! Extensions from @primer/css/alerts/flash.scss */
  --color-social-reaction-bg-hover: var(--color-scale-gray-1);
  --color-social-reaction-bg-reacted-hover: var(--color-scale-blue-1);
}

main .pagination-loader-container {
  background-image: url("https://github.com/images/modules/pulls/progressive-disclosure-line.svg");
}

main .gsc-loading-image {
  background-image: url("https://github.githubassets.com/images/mona-loading-default.gif");
}
