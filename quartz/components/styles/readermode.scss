.readermode {
  cursor: pointer;
  padding: 0;
  position: relative;
  background: none;
  border: none;
  width: 20px;
  height: 20px;
  margin: 0;
  text-align: inherit;
  flex-shrink: 0;

  & svg {
    position: absolute;
    width: 20px;
    height: 20px;
    top: calc(50% - 10px);
    fill: var(--darkgray);
    stroke: var(--darkgray);
    transition: opacity 0.1s ease;
  }
}

:root[reader-mode="on"] {
  & .sidebar.left,
  & .sidebar.right {
    opacity: 0;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 1;
    }
  }
}
