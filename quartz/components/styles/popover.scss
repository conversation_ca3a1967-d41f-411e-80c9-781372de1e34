@use "../../styles/variables.scss" as *;

@keyframes dropin {
  0% {
    opacity: 0;
    visibility: hidden;
  }
  1% {
    opacity: 0;
  }
  100% {
    opacity: 1;
    visibility: visible;
  }
}

.popover {
  z-index: 999;
  position: fixed;
  overflow: visible;
  padding: 1rem;
  left: 0;
  top: 0;
  will-change: transform;

  & > .popover-inner {
    position: relative;
    width: 30rem;
    max-height: 20rem;
    padding: 0 1rem 1rem 1rem;
    font-weight: initial;
    font-style: initial;
    line-height: normal;
    font-size: initial;
    font-family: var(--bodyFont);
    border: 1px solid var(--lightgray);
    background-color: var(--light);
    border-radius: 5px;
    box-shadow: 6px 6px 36px 0 rgba(0, 0, 0, 0.25);
    overflow: auto;
    overscroll-behavior: contain;
    white-space: normal;
    user-select: none;
    cursor: default;
  }

  & > .popover-inner[data-content-type] {
    &[data-content-type*="pdf"],
    &[data-content-type*="image"] {
      padding: 0;
      max-height: 100%;
    }

    &[data-content-type*="image"] {
      img {
        margin: 0;
        border-radius: 0;
        display: block;
      }
    }

    &[data-content-type*="pdf"] {
      iframe {
        width: 100%;
      }
    }
  }

  h1 {
    font-size: 1.5rem;
  }

  visibility: hidden;
  opacity: 0;
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease;

  @media all and ($mobile) {
    display: none !important;
  }
}

.active-popover,
.popover:hover {
  animation: dropin 0.3s ease;
  animation-fill-mode: forwards;
  animation-delay: 0.2s;
}
