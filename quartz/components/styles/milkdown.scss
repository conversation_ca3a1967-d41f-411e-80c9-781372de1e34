@use "../../styles/variables.scss" as *;

// Styles for the new inline content editor
.milkdown-content-wrapper {
  position: relative;

  .milkdown-edit-controls {
    position: absolute;
    top: -40px;
    right: 0;
    display: flex;
    gap: 0.5rem;
    z-index: 10;

    .milkdown-edit-btn,
    .milkdown-save-btn {
      display: inline-flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.25rem 0.5rem;
      border: 1px solid var(--border);
      border-radius: 4px;
      background: var(--light);
      color: var(--dark);
      font-size: 0.8rem;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);

      &:hover {
        background: var(--lightgray);
        border-color: var(--secondary);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
      }

      &.active {
        background: var(--secondary);
        color: var(--light);
        border-color: var(--secondary);
      }

      svg {
        width: 14px;
        height: 14px;
      }
    }

    .milkdown-save-btn {
      background: var(--secondary);
      color: var(--light);
      border-color: var(--secondary);

      &:hover {
        background: var(--tertiary);
        border-color: var(--tertiary);
      }
    }
  }

  .milkdown-editable-content {
    position: relative;
    transition: all 0.3s ease;

    &:hover .milkdown-edit-controls {
      opacity: 1;
    }
  }

  .milkdown-inline-editor {
    font-family: var(--codeFont) !important;
    transition: all 0.3s ease;

    &:focus {
      border-color: var(--secondary) !important;
      box-shadow: 0 0 0 3px rgba(var(--secondary-rgb), 0.1) !important;
    }
  }
}

// Legacy styles for the old container approach
.milkdown-container {
  margin: 1rem 0;
  border: 1px solid var(--border);
  border-radius: 8px;
  overflow: hidden;
  background: var(--light);
  
  .milkdown-toolbar {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--lightgray);
    border-bottom: 1px solid var(--border);
    
    .milkdown-toggle-btn,
    .milkdown-save-btn {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.25rem 0.5rem;
      border: 1px solid var(--border);
      border-radius: 4px;
      background: var(--light);
      color: var(--dark);
      font-size: 0.8rem;
      cursor: pointer;
      transition: all 0.2s ease;
      
      &:hover {
        background: var(--gray);
        border-color: var(--darkgray);
      }
      
      &.active {
        background: var(--secondary);
        color: var(--light);
        border-color: var(--secondary);
      }
      
      svg {
        width: 14px;
        height: 14px;
      }
    }
  }
  
  .milkdown-editor-wrapper {
    position: relative;
    min-height: 200px;
    
    .milkdown-editor {
      padding: 1rem;
      min-height: 200px;

      // Basic Milkdown editor styles
      .milkdown,
      .crepe {
        background: transparent;
        color: var(--dark);
        font-family: var(--bodyFont);
        font-size: 1rem;
        line-height: 1.6;
        border: none;
        outline: none;
        width: 100%;
        min-height: 200px;
        
        .editor {
          outline: none;
          
          h1, h2, h3, h4, h5, h6 {
            color: var(--dark);
            font-family: var(--headerFont);
            margin: 1.5rem 0 0.5rem 0;
            
            &:first-child {
              margin-top: 0;
            }
          }
          
          h1 { font-size: 1.75rem; }
          h2 { font-size: 1.5rem; }
          h3 { font-size: 1.25rem; }
          h4 { font-size: 1.1rem; }
          h5 { font-size: 1rem; }
          h6 { font-size: 0.9rem; }
          
          p {
            margin: 0.5rem 0;
            color: var(--dark);
          }
          
          a {
            color: var(--secondary);
            text-decoration: none;
            
            &:hover {
              text-decoration: underline;
            }
          }
          
          code {
            background: var(--lightgray);
            padding: 0.1rem 0.3rem;
            border-radius: 3px;
            font-family: var(--codeFont);
            font-size: 0.9em;
          }
          
          pre {
            background: var(--lightgray);
            padding: 1rem;
            border-radius: 5px;
            overflow-x: auto;
            margin: 1rem 0;
            
            code {
              background: transparent;
              padding: 0;
            }
          }
          
          blockquote {
            border-left: 3px solid var(--secondary);
            padding-left: 1rem;
            margin: 1rem 0;
            color: var(--gray);
            font-style: italic;
          }
          
          ul, ol {
            padding-left: 1.5rem;
            margin: 0.5rem 0;
          }
          
          li {
            margin: 0.25rem 0;
          }
          
          table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            
            th, td {
              border: 1px solid var(--border);
              padding: 0.5rem;
              text-align: left;
            }
            
            th {
              background: var(--lightgray);
              font-weight: 600;
            }
          }
          
          hr {
            border: none;
            border-top: 1px solid var(--border);
            margin: 2rem 0;
          }
        }
      }
    }
    
    .milkdown-preview {
      padding: 1rem;
      min-height: 200px;
      color: var(--dark);
      font-family: var(--bodyFont);
      line-height: 1.6;
    }
  }
  
  .milkdown-shortcuts-hint {
    padding: 0.5rem 1rem;
    background: var(--lightgray);
    border-top: 1px solid var(--border);
    font-size: 0.75rem;
    color: var(--gray);
    
    kbd {
      background: var(--light);
      border: 1px solid var(--border);
      border-radius: 3px;
      padding: 0.1rem 0.3rem;
      font-size: 0.7rem;
      font-family: var(--codeFont);
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  :root[saved-theme="dark"] .milkdown-container {
    .milkdown-editor .milkdown .editor {
      h1, h2, h3, h4, h5, h6, p {
        color: var(--light);
      }
    }
    
    .milkdown-preview {
      color: var(--light);
    }
  }
}

// Mobile responsiveness
@media (max-width: 768px) {
  .milkdown-container {
    margin: 0.5rem 0;
    border-radius: 4px;
    
    .milkdown-toolbar {
      padding: 0.5rem;
      flex-wrap: wrap;
    }
    
    .milkdown-editor-wrapper {
      .milkdown-editor,
      .milkdown-preview {
        padding: 0.75rem;
      }
    }
    
    .milkdown-shortcuts-hint {
      display: none; // Hide shortcuts hint on mobile
    }
  }
}
