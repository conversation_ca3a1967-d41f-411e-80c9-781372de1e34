.expand-button {
  position: absolute;
  display: flex;
  float: right;
  padding: 0.4rem;
  margin: 0.3rem;
  right: 0; // NOTE: right will be set in mermaid.inline.ts
  color: var(--gray);
  border-color: var(--dark);
  background-color: var(--light);
  border: 1px solid;
  border-radius: 5px;
  opacity: 0;
  transition: 0.2s;

  & > svg {
    fill: var(--light);
    filter: contrast(0.3);
  }

  &:hover {
    cursor: pointer;
    border-color: var(--secondary);
  }

  &:focus {
    outline: 0;
  }
}

pre {
  &:hover > .expand-button {
    opacity: 1;
    transition: 0.2s;
  }
}

#mermaid-container {
  position: fixed;
  contain: layout;
  z-index: 999;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: none;
  backdrop-filter: blur(4px);
  background: rgba(0, 0, 0, 0.5);

  &.active {
    display: inline-block;
  }

  & > #mermaid-space {
    border: 1px solid var(--lightgray);
    background-color: var(--light);
    border-radius: 5px;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    height: 80vh;
    width: 80vw;
    overflow: hidden;

    & > .mermaid-content {
      padding: 2rem;
      position: relative;
      transform-origin: 0 0;
      transition: transform 0.1s ease;
      overflow: visible;
      min-height: 200px;
      min-width: 200px;

      pre {
        margin: 0;
        border: none;
      }

      svg {
        max-width: none;
        height: auto;
      }
    }

    & > .mermaid-controls {
      position: absolute;
      bottom: 20px;
      right: 20px;
      display: flex;
      gap: 8px;
      padding: 8px;
      background: var(--light);
      border: 1px solid var(--lightgray);
      border-radius: 6px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      z-index: 2;

      .mermaid-control-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        padding: 0;
        border: 1px solid var(--lightgray);
        background: var(--light);
        color: var(--dark);
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        font-family: var(--bodyFont);
        transition: all 0.2s ease;

        &:hover {
          background: var(--lightgray);
        }

        &:active {
          transform: translateY(1px);
        }

        // Style the reset button differently
        &:nth-child(2) {
          width: auto;
          padding: 0 12px;
          font-size: 14px;
        }
      }
    }
  }
}
