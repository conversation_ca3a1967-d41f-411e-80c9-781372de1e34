import { QuartzComponent, QuartzComponentConstructor, QuartzComponentProps } from "./types"
import { classNames } from "../util/lang"
// @ts-ignore
import milkdownScript from "./scripts/milkdown.inline"
import milkdownStyles from "./styles/milkdown.scss"

interface MilkdownOptions {
  showToggle?: boolean
  defaultMode?: "view" | "edit"
  enableShortcuts?: boolean
}

const defaultOptions: MilkdownOptions = {
  showToggle: true,
  defaultMode: "view",
  enableShortcuts: true,
}

export default ((userOpts?: Partial<MilkdownOptions>) => {
  const opts = { ...defaultOptions, ...userOpts }
  
  const MilkdownEditor: QuartzComponent = ({ displayClass, fileData }: QuartzComponentProps) => {
    const content = fileData.text || ""

    if (!opts.showToggle && opts.defaultMode === "view") {
      return null
    }

    return (
      <div class={classNames(displayClass, "milkdown-container")}>
        {opts.showToggle && (
          <div class="milkdown-toolbar">
            <button
              class="milkdown-toggle-btn"
              data-mode="view"
              title="Toggle Edit Mode"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
              </svg>
              Edit
            </button>
            <button
              class="milkdown-save-btn"
              style="display: none;"
              title="Save Changes"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
                <polyline points="17,21 17,13 7,13 7,21"/>
                <polyline points="7,3 7,8 15,8"/>
              </svg>
              Save
            </button>
          </div>
        )}
        
        <div 
          class="milkdown-editor-wrapper"
          data-content={content}
          data-file-path={fileData.filePath}
          data-slug={fileData.slug}
          data-default-mode={opts.defaultMode}
          data-enable-shortcuts={opts.enableShortcuts}
        >
          <div class="milkdown-editor" id={`milkdown-${fileData.slug}`}></div>
          <div class="milkdown-preview" style={opts.defaultMode === "edit" ? "display: none;" : ""}>
            {/* Preview content will be populated by the script */}
          </div>
        </div>
        
        {opts.enableShortcuts && (
          <div class="milkdown-shortcuts-hint">
            <small>
              Press <kbd>Ctrl</kbd>+<kbd>E</kbd> to toggle edit mode, <kbd>Ctrl</kbd>+<kbd>S</kbd> to save
            </small>
          </div>
        )}
      </div>
    )
  }

  MilkdownEditor.css = milkdownStyles
  MilkdownEditor.afterDOMLoaded = milkdownScript
  
  return MilkdownEditor
}) satisfies QuartzComponentConstructor<Partial<MilkdownOptions>>
