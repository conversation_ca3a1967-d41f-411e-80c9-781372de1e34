interface ContentState {
  isEditing: boolean
  originalMarkdown: string
  hasChanges: boolean
  milkdownEditor: any | null
  contentElement: HTMLElement | null
  originalHTML: string
}

const contentStates = new Map<string, ContentState>()

// Load Milkdown dynamically
async function loadMilkdown() {
  try {
    const { Editor, rootCtx, defaultValueCtx } = await import('@milkdown/kit/core')
    const { commonmark } = await import('@milkdown/kit/preset/commonmark')
    const { nord } = await import('@milkdown/theme-nord')
    const { listener, listenerCtx } = await import('@milkdown/kit/plugin/listener')

    return { Editor, rootCtx, defaultValueCtx, commonmark, nord, listener, listenerCtx }
  } catch (error) {
    console.warn('Could not load Milkdown, falling back to simple contenteditable:', error)
    return null
  }
}

async function initializeMilkdownContent() {
  const contentElements = document.querySelectorAll('.milkdown-editable-content')

  contentElements.forEach(async (element) => {
    const contentEl = element as HTMLElement
    const slug = contentEl.getAttribute('data-slug') || ''
    const filePath = contentEl.getAttribute('data-file-path') || ''
    const markdownContent = contentEl.getAttribute('data-markdown-content') || ''

    // Initialize state
    const state: ContentState = {
      isEditing: false,
      originalMarkdown: markdownContent,
      hasChanges: false,
      milkdownEditor: null,
      contentElement: contentEl,
      originalHTML: contentEl.innerHTML
    }

    contentStates.set(slug, state)
    
    // Set up edit button
    const editBtn = document.querySelector(`[data-slug="${slug}"].milkdown-edit-btn`) as HTMLButtonElement
    const saveBtn = document.querySelector(`[data-slug="${slug}"].milkdown-save-btn`) as HTMLButtonElement
    
    if (editBtn) {
      editBtn.addEventListener('click', () => toggleEditMode(slug))
    }
    
    if (saveBtn) {
      saveBtn.addEventListener('click', () => saveContent(slug))
    }
    
    // Set up keyboard shortcuts
    setupKeyboardShortcuts(slug)
  })
}

function toggleEditMode(slug: string) {
  const state = contentStates.get(slug)
  if (!state || !state.contentElement) return
  
  const editBtn = document.querySelector(`[data-slug="${slug}"].milkdown-edit-btn`) as HTMLButtonElement
  const saveBtn = document.querySelector(`[data-slug="${slug}"].milkdown-save-btn`) as HTMLButtonElement
  
  if (!state.isEditing) {
    // Switch to edit mode
    enterEditMode(slug)
    if (editBtn) {
      editBtn.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
          <circle cx="12" cy="12" r="3"/>
        </svg>
        View
      `
      editBtn.classList.add('active')
    }
  } else {
    // Switch to view mode
    exitEditMode(slug)
    if (editBtn) {
      editBtn.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
          <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
        </svg>
        Edit
      `
      editBtn.classList.remove('active')
    }
  }
  
  // Update save button visibility
  updateSaveButton(slug)
}

async function enterEditMode(slug: string) {
  const state = contentStates.get(slug)
  if (!state || !state.contentElement) return

  state.isEditing = true

  // Try to load Milkdown for WYSIWYG editing
  const milkdown = await loadMilkdown()

  if (milkdown) {
    await initializeMilkdownEditor(slug, milkdown)
  } else {
    // Fallback to contenteditable
    initializeContentEditableEditor(slug)
  }
}

async function initializeMilkdownEditor(slug: string, milkdown: any) {
  const state = contentStates.get(slug)
  if (!state || !state.contentElement) return

  try {
    const { Editor, rootCtx, defaultValueCtx, commonmark, nord, listener, listenerCtx } = milkdown

    // Clear the content element and prepare for Milkdown
    state.contentElement.innerHTML = ''
    state.contentElement.style.border = '2px solid var(--secondary)'
    state.contentElement.style.borderRadius = '8px'
    state.contentElement.style.padding = '1rem'

    // Create Milkdown editor
    const editor = await Editor.make()
      .config((ctx) => {
        ctx.set(rootCtx, state.contentElement)
        ctx.set(defaultValueCtx, state.originalMarkdown)

        // Set up change listener
        ctx.get(listenerCtx).markdownUpdated((ctx, markdown) => {
          state.hasChanges = markdown !== state.originalMarkdown
          updateSaveButton(slug)
        })
      })
      .use(commonmark)
      .use(nord)
      .use(listener)
      .create()

    state.milkdownEditor = editor

  } catch (error) {
    console.error('Failed to initialize Milkdown editor:', error)
    // Fallback to contenteditable
    initializeContentEditableEditor(slug)
  }
}

function initializeContentEditableEditor(slug: string) {
  const state = contentStates.get(slug)
  if (!state || !state.contentElement) return

  // Make the content directly editable
  state.contentElement.contentEditable = 'true'
  state.contentElement.style.border = '2px solid var(--secondary)'
  state.contentElement.style.borderRadius = '8px'
  state.contentElement.style.padding = '1rem'
  state.contentElement.style.outline = 'none'
  state.contentElement.style.minHeight = '200px'

  // Add some basic styling for editing
  state.contentElement.style.background = 'var(--light)'
  state.contentElement.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)'

  // Set up change detection
  const handleInput = () => {
    state.hasChanges = true
    updateSaveButton(slug)
  }

  state.contentElement.addEventListener('input', handleInput)
  state.contentElement.addEventListener('paste', handleInput)

  // Focus the editor
  state.contentElement.focus()
}

async function exitEditMode(slug: string) {
  const state = contentStates.get(slug)
  if (!state || !state.contentElement) return

  state.isEditing = false

  // Reset styling
  state.contentElement.style.border = 'none'
  state.contentElement.style.borderRadius = ''
  state.contentElement.style.padding = ''
  state.contentElement.style.outline = ''
  state.contentElement.style.minHeight = ''
  state.contentElement.style.background = ''
  state.contentElement.style.boxShadow = ''

  if (state.milkdownEditor) {
    // If using Milkdown, get the markdown and convert back to HTML
    try {
      const markdown = await state.milkdownEditor.action((ctx: any) => {
        return ctx.get('markdownSerializer').serialize(ctx.get('doc'))
      })

      if (markdown !== state.originalMarkdown) {
        // Update the content with new HTML
        state.contentElement.innerHTML = markdownToHtml(markdown)
      } else {
        // Restore original HTML
        state.contentElement.innerHTML = state.originalHTML
      }

      // Destroy the Milkdown editor
      state.milkdownEditor.destroy()
      state.milkdownEditor = null
    } catch (error) {
      console.error('Error getting content from Milkdown:', error)
      state.contentElement.innerHTML = state.originalHTML
    }
  } else {
    // If using contenteditable, just disable editing
    state.contentElement.contentEditable = 'false'

    // The content is already updated in place, no need to do anything else
  }
}

function updatePreview(slug: string, markdown: string) {
  const state = contentStates.get(slug)
  if (!state || !state.contentElement) return
  
  // Simple markdown to HTML conversion for preview
  const html = markdownToHtml(markdown)
  state.contentElement.innerHTML = html
}

async function saveContent(slug: string) {
  const state = contentStates.get(slug)
  if (!state) return

  let currentContent = ''

  if (state.milkdownEditor) {
    // Get content from Milkdown editor
    try {
      currentContent = await state.milkdownEditor.action((ctx: any) => {
        return ctx.get('markdownSerializer').serialize(ctx.get('doc'))
      })
    } catch (error) {
      console.error('Error getting content from Milkdown:', error)
      return
    }
  } else if (state.contentElement) {
    // Get content from contenteditable
    currentContent = htmlToMarkdown(state.contentElement.innerHTML)
  }

  // Update the original content
  state.originalMarkdown = currentContent
  state.hasChanges = false

  // Update the content element's data attribute
  if (state.contentElement) {
    state.contentElement.setAttribute('data-markdown-content', currentContent)
    state.originalHTML = state.contentElement.innerHTML
  }

  // Show save confirmation
  showNotification('Content saved successfully!', 'success')

  // Update buttons
  updateSaveButton(slug)
}

function updateSaveButton(slug: string) {
  const state = contentStates.get(slug)
  const saveBtn = document.querySelector(`[data-slug="${slug}"].milkdown-save-btn`) as HTMLButtonElement
  
  if (saveBtn && state) {
    if (state.isEditing && state.hasChanges) {
      saveBtn.style.display = 'inline-flex'
    } else {
      saveBtn.style.display = 'none'
    }
  }
}

function setupKeyboardShortcuts(slug: string) {
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey || e.metaKey) {
      if (e.key === 'e') {
        e.preventDefault()
        toggleEditMode(slug)
      } else if (e.key === 's') {
        e.preventDefault()
        const state = contentStates.get(slug)
        if (state?.isEditing && state.hasChanges) {
          saveContent(slug)
        }
      }
    }
  })
}

function markdownToHtml(markdown: string): string {
  // Enhanced markdown to HTML conversion
  let html = markdown
    // Headers
    .replace(/^### (.*$)/gim, '<h3 id="$1">$1</h3>')
    .replace(/^## (.*$)/gim, '<h2 id="$1">$1</h2>')
    .replace(/^# (.*$)/gim, '<h1 id="$1">$1</h1>')

    // Bold and italic
    .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/gim, '<em>$1</em>')

    // Code
    .replace(/`(.*?)`/gim, '<code>$1</code>')

    // Links
    .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2">$1</a>')

    // Lists
    .replace(/^\* (.*$)/gim, '<li>$1</li>')
    .replace(/^- (.*$)/gim, '<li>$1</li>')

    // Paragraphs
    .replace(/\n\n/gim, '</p><p>')
    .replace(/\n/gim, '<br>')

  // Wrap in paragraphs and clean up
  html = '<p>' + html + '</p>'
  html = html
    .replace(/<p><h([1-6])/gim, '<h$1')
    .replace(/<\/h([1-6])><\/p>/gim, '</h$1>')
    .replace(/<p><li>/gim, '<ul><li>')
    .replace(/<\/li><\/p>/gim, '</li></ul>')
    .replace(/<\/ul><ul>/gim, '')

  return html
}

function htmlToMarkdown(html: string): string {
  // Simple HTML to markdown conversion
  return html
    // Headers
    .replace(/<h1[^>]*>(.*?)<\/h1>/gim, '# $1')
    .replace(/<h2[^>]*>(.*?)<\/h2>/gim, '## $1')
    .replace(/<h3[^>]*>(.*?)<\/h3>/gim, '### $1')
    .replace(/<h4[^>]*>(.*?)<\/h4>/gim, '#### $1')
    .replace(/<h5[^>]*>(.*?)<\/h5>/gim, '##### $1')
    .replace(/<h6[^>]*>(.*?)<\/h6>/gim, '###### $1')

    // Bold and italic
    .replace(/<strong[^>]*>(.*?)<\/strong>/gim, '**$1**')
    .replace(/<b[^>]*>(.*?)<\/b>/gim, '**$1**')
    .replace(/<em[^>]*>(.*?)<\/em>/gim, '*$1*')
    .replace(/<i[^>]*>(.*?)<\/i>/gim, '*$1*')

    // Code
    .replace(/<code[^>]*>(.*?)<\/code>/gim, '`$1`')

    // Links
    .replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gim, '[$2]($1)')

    // Lists
    .replace(/<li[^>]*>(.*?)<\/li>/gim, '- $1')
    .replace(/<ul[^>]*>/gim, '')
    .replace(/<\/ul>/gim, '')
    .replace(/<ol[^>]*>/gim, '')
    .replace(/<\/ol>/gim, '')

    // Paragraphs and breaks
    .replace(/<p[^>]*>/gim, '')
    .replace(/<\/p>/gim, '\n\n')
    .replace(/<br[^>]*>/gim, '\n')

    // Clean up extra whitespace
    .replace(/\n\n\n+/gim, '\n\n')
    .trim()
}

function showNotification(message: string, type: 'success' | 'error' | 'info' = 'info') {
  const notification = document.createElement('div')
  notification.className = `milkdown-notification milkdown-notification-${type}`
  notification.textContent = message
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
    color: white;
    border-radius: 4px;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    animation: slideIn 0.3s ease;
  `
  
  document.body.appendChild(notification)
  
  setTimeout(() => {
    notification.style.animation = 'slideOut 0.3s ease'
    setTimeout(() => notification.remove(), 300)
  }, 3000)
}

// Add CSS animations
const style = document.createElement('style')
style.textContent = `
  @keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }
  @keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
  }
`
document.head.appendChild(style)

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeMilkdownContent)

// Re-initialize when navigating in SPA mode
document.addEventListener('nav', initializeMilkdownContent)
