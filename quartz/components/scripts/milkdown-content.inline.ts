interface ContentState {
  isEditing: boolean
  originalMarkdown: string
  hasChanges: boolean
  editorElement: HTMLElement | null
  contentElement: HTMLElement | null
}

const contentStates = new Map<string, ContentState>()

function initializeMilkdownContent() {
  const contentElements = document.querySelectorAll('.milkdown-editable-content')
  
  contentElements.forEach((element) => {
    const contentEl = element as HTMLElement
    const slug = contentEl.getAttribute('data-slug') || ''
    const filePath = contentEl.getAttribute('data-file-path') || ''
    const markdownContent = contentEl.getAttribute('data-markdown-content') || ''
    
    // Initialize state
    const state: ContentState = {
      isEditing: false,
      originalMarkdown: markdownContent,
      hasChanges: false,
      editorElement: null,
      contentElement: contentEl
    }
    
    contentStates.set(slug, state)
    
    // Set up edit button
    const editBtn = document.querySelector(`[data-slug="${slug}"].milkdown-edit-btn`) as HTMLButtonElement
    const saveBtn = document.querySelector(`[data-slug="${slug}"].milkdown-save-btn`) as HTMLButtonElement
    
    if (editBtn) {
      editBtn.addEventListener('click', () => toggleEditMode(slug))
    }
    
    if (saveBtn) {
      saveBtn.addEventListener('click', () => saveContent(slug))
    }
    
    // Set up keyboard shortcuts
    setupKeyboardShortcuts(slug)
  })
}

function toggleEditMode(slug: string) {
  const state = contentStates.get(slug)
  if (!state || !state.contentElement) return
  
  const editBtn = document.querySelector(`[data-slug="${slug}"].milkdown-edit-btn`) as HTMLButtonElement
  const saveBtn = document.querySelector(`[data-slug="${slug}"].milkdown-save-btn`) as HTMLButtonElement
  
  if (!state.isEditing) {
    // Switch to edit mode
    enterEditMode(slug)
    if (editBtn) {
      editBtn.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
          <circle cx="12" cy="12" r="3"/>
        </svg>
        View
      `
      editBtn.classList.add('active')
    }
  } else {
    // Switch to view mode
    exitEditMode(slug)
    if (editBtn) {
      editBtn.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
          <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
        </svg>
        Edit
      `
      editBtn.classList.remove('active')
    }
  }
  
  // Update save button visibility
  updateSaveButton(slug)
}

function enterEditMode(slug: string) {
  const state = contentStates.get(slug)
  if (!state || !state.contentElement) return
  
  state.isEditing = true
  
  // Create textarea editor
  const textarea = document.createElement('textarea')
  textarea.className = 'milkdown-inline-editor'
  textarea.value = state.originalMarkdown
  textarea.style.cssText = `
    width: 100%;
    min-height: 300px;
    padding: 1rem;
    border: 2px solid var(--secondary);
    border-radius: 8px;
    font-family: var(--codeFont);
    font-size: 0.9rem;
    line-height: 1.6;
    background: var(--light);
    color: var(--dark);
    resize: vertical;
    outline: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  `
  
  // Auto-resize functionality
  const autoResize = () => {
    textarea.style.height = 'auto'
    textarea.style.height = Math.max(300, textarea.scrollHeight) + 'px'
  }
  
  textarea.addEventListener('input', () => {
    autoResize()
    state.hasChanges = textarea.value !== state.originalMarkdown
    updateSaveButton(slug)
  })
  
  // Initial resize
  setTimeout(autoResize, 10)
  
  // Hide original content and show editor
  state.contentElement.style.display = 'none'
  state.contentElement.parentNode?.insertBefore(textarea, state.contentElement.nextSibling)
  state.editorElement = textarea
  
  // Focus the editor
  textarea.focus()
}

function exitEditMode(slug: string) {
  const state = contentStates.get(slug)
  if (!state || !state.editorElement || !state.contentElement) return
  
  state.isEditing = false
  
  // Get current content from editor
  const currentMarkdown = (state.editorElement as HTMLTextAreaElement).value
  
  // Update preview if content changed
  if (currentMarkdown !== state.originalMarkdown) {
    updatePreview(slug, currentMarkdown)
  }
  
  // Remove editor and show original content
  state.editorElement.remove()
  state.editorElement = null
  state.contentElement.style.display = 'block'
}

function updatePreview(slug: string, markdown: string) {
  const state = contentStates.get(slug)
  if (!state || !state.contentElement) return
  
  // Simple markdown to HTML conversion for preview
  const html = markdownToHtml(markdown)
  state.contentElement.innerHTML = html
}

function saveContent(slug: string) {
  const state = contentStates.get(slug)
  if (!state || !state.editorElement) return
  
  const currentMarkdown = (state.editorElement as HTMLTextAreaElement).value
  
  // Update the original content
  state.originalMarkdown = currentMarkdown
  state.hasChanges = false
  
  // Update the content element's data attribute
  if (state.contentElement) {
    state.contentElement.setAttribute('data-markdown-content', currentMarkdown)
  }
  
  // Show save confirmation
  showNotification('Content saved successfully!', 'success')
  
  // Update buttons
  updateSaveButton(slug)
  
  // Update preview
  updatePreview(slug, currentMarkdown)
}

function updateSaveButton(slug: string) {
  const state = contentStates.get(slug)
  const saveBtn = document.querySelector(`[data-slug="${slug}"].milkdown-save-btn`) as HTMLButtonElement
  
  if (saveBtn && state) {
    if (state.isEditing && state.hasChanges) {
      saveBtn.style.display = 'inline-flex'
    } else {
      saveBtn.style.display = 'none'
    }
  }
}

function setupKeyboardShortcuts(slug: string) {
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey || e.metaKey) {
      if (e.key === 'e') {
        e.preventDefault()
        toggleEditMode(slug)
      } else if (e.key === 's') {
        e.preventDefault()
        const state = contentStates.get(slug)
        if (state?.isEditing && state.hasChanges) {
          saveContent(slug)
        }
      }
    }
  })
}

function markdownToHtml(markdown: string): string {
  // Simple markdown to HTML conversion
  return markdown
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$2</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/gim, '<em>$1</em>')
    .replace(/`(.*?)`/gim, '<code>$1</code>')
    .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2">$1</a>')
    .replace(/\n\n/gim, '</p><p>')
    .replace(/\n/gim, '<br>')
    .replace(/^(.+)$/gim, '<p>$1</p>')
    .replace(/<p><h([1-6])>/gim, '<h$1>')
    .replace(/<\/h([1-6])><\/p>/gim, '</h$1>')
}

function showNotification(message: string, type: 'success' | 'error' | 'info' = 'info') {
  const notification = document.createElement('div')
  notification.className = `milkdown-notification milkdown-notification-${type}`
  notification.textContent = message
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
    color: white;
    border-radius: 4px;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    animation: slideIn 0.3s ease;
  `
  
  document.body.appendChild(notification)
  
  setTimeout(() => {
    notification.style.animation = 'slideOut 0.3s ease'
    setTimeout(() => notification.remove(), 300)
  }, 3000)
}

// Add CSS animations
const style = document.createElement('style')
style.textContent = `
  @keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }
  @keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
  }
`
document.head.appendChild(style)

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeMilkdownContent)

// Re-initialize when navigating in SPA mode
document.addEventListener('nav', initializeMilkdownContent)
