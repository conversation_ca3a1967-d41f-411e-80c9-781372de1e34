interface MilkdownState {
  editor: any | null
  isEditMode: boolean
  originalContent: string
  hasChanges: boolean
  textarea: HTMLTextAreaElement | null
}

const milkdownInstances = new Map<string, MilkdownState>()

// Simple function to check if we're in edit mode
function isInEditMode(instanceId: string): boolean {
  const state = milkdownInstances.get(instanceId)
  return state ? state.isEditMode : false
}

function initializeMilkdown() {
  const containers = document.querySelectorAll('.milkdown-editor-wrapper')
  
  containers.forEach((container) => {
    const editorElement = container.querySelector('.milkdown-editor') as HTMLElement
    const previewElement = container.querySelector('.milkdown-preview') as HTMLElement
    const toggleBtn = container.parentElement?.querySelector('.milkdown-toggle-btn') as HTMLButtonElement
    const saveBtn = container.parentElement?.querySelector('.milkdown-save-btn') as HTMLButtonElement
    
    if (!editorElement) return
    
    const content = container.getAttribute('data-content') || ''
    const filePath = container.getAttribute('data-file-path') || ''
    const slug = container.getAttribute('data-slug') || ''
    const defaultMode = container.getAttribute('data-default-mode') || 'view'
    const enableShortcuts = container.getAttribute('data-enable-shortcuts') === 'true'
    
    const instanceId = slug || filePath
    
    // Initialize state
    const state: MilkdownState = {
      editor: null,
      isEditMode: defaultMode === 'edit',
      originalContent: content,
      hasChanges: false,
      textarea: null
    }

    milkdownInstances.set(instanceId, state)

    // Set initial preview content
    if (previewElement && content) {
      previewElement.innerHTML = markdownToHtml(content)
    }

    // Initialize with simple textarea editor
    initializeSimpleEditor(editorElement, content, instanceId)
    
    // Set up toggle button
    if (toggleBtn) {
      toggleBtn.addEventListener('click', () => toggleEditMode(instanceId))
      updateToggleButton(toggleBtn, state.isEditMode)
    }
    
    // Set up save button
    if (saveBtn) {
      saveBtn.addEventListener('click', () => saveContent(instanceId))
    }
    
    // Set up keyboard shortcuts
    if (enableShortcuts) {
      setupKeyboardShortcuts(instanceId)
    }
    
    // Update UI based on initial mode
    updateUI(instanceId)
  })
}

function initializeSimpleEditor(element: HTMLElement, content: string, instanceId: string) {
  const state = milkdownInstances.get(instanceId)
  if (!state) return

  // Create a simple textarea editor
  element.innerHTML = `
    <textarea
      class="milkdown-textarea"
      style="width: 100%; min-height: 200px; padding: 1rem; border: 1px solid var(--border); border-radius: 4px; font-family: var(--codeFont); font-size: 0.9rem; line-height: 1.6; background: var(--light); color: var(--dark); resize: vertical; display: ${state.isEditMode ? 'block' : 'none'};"
      placeholder="Edit your markdown here..."
    >${content}</textarea>
  `

  const textarea = element.querySelector('.milkdown-textarea') as HTMLTextAreaElement
  if (textarea) {
    state.textarea = textarea
    state.editor = {
      getMarkdown: () => textarea.value,
      setMarkdown: (value: string) => { textarea.value = value }
    }

    // Set up change detection
    textarea.addEventListener('input', () => {
      state.hasChanges = textarea.value !== state.originalContent
      updateSaveButton(instanceId)
    })

    // Auto-resize textarea
    textarea.addEventListener('input', () => {
      textarea.style.height = 'auto'
      textarea.style.height = Math.max(200, textarea.scrollHeight) + 'px'
    })
  }
}

function toggleEditMode(instanceId: string) {
  const state = milkdownInstances.get(instanceId)
  if (!state) return
  
  const container = document.querySelector(`[data-slug="${instanceId}"]`) as HTMLElement
  if (!container) return
  
  const editorElement = container.querySelector('.milkdown-editor') as HTMLElement
  const previewElement = container.querySelector('.milkdown-preview') as HTMLElement
  const toggleBtn = container.parentElement?.querySelector('.milkdown-toggle-btn') as HTMLButtonElement
  
  state.isEditMode = !state.isEditMode
  
  if (state.isEditMode) {
    // Switch to edit mode - show textarea
    if (state.textarea) {
      state.textarea.style.display = 'block'
    }
  } else {
    // Switch to view mode - hide textarea and update preview
    if (state.textarea) {
      state.textarea.style.display = 'none'
    }
    if (state.editor && previewElement) {
      const currentContent = state.editor.getMarkdown()
      previewElement.innerHTML = markdownToHtml(currentContent)
    }
  }
  
  updateToggleButton(toggleBtn, state.isEditMode)
  updateUI(instanceId)
}

function saveContent(instanceId: string) {
  const state = milkdownInstances.get(instanceId)
  if (!state || !state.editor) return

  let currentContent: string
  try {
    currentContent = state.editor.getMarkdown()
  } catch (error) {
    console.error('Could not get markdown content for saving:', error)
    return
  }
  
  // Here you would typically send the content to a server
  // For now, we'll just update the original content and show a message
  state.originalContent = currentContent
  state.hasChanges = false
  
  // Show save confirmation
  showNotification('Content saved successfully!', 'success')
  updateSaveButton(instanceId)
  
  // Update preview
  const container = document.querySelector(`[data-slug="${instanceId}"]`) as HTMLElement
  const previewElement = container?.querySelector('.milkdown-preview') as HTMLElement
  if (previewElement) {
    previewElement.innerHTML = markdownToHtml(currentContent)
  }
}

function setupKeyboardShortcuts(instanceId: string) {
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey || e.metaKey) {
      if (e.key === 'e') {
        e.preventDefault()
        toggleEditMode(instanceId)
      } else if (e.key === 's') {
        e.preventDefault()
        const state = milkdownInstances.get(instanceId)
        if (state?.isEditMode && state.hasChanges) {
          saveContent(instanceId)
        }
      }
    }
  })
}

function updateUI(instanceId: string) {
  const state = milkdownInstances.get(instanceId)
  if (!state) return
  
  const container = document.querySelector(`[data-slug="${instanceId}"]`) as HTMLElement
  if (!container) return
  
  const editorElement = container.querySelector('.milkdown-editor') as HTMLElement
  const previewElement = container.querySelector('.milkdown-preview') as HTMLElement
  
  if (state.isEditMode) {
    editorElement.style.display = 'block'
    if (previewElement) previewElement.style.display = 'none'
  } else {
    editorElement.style.display = 'none'
    if (previewElement) previewElement.style.display = 'block'
  }
}

function updateToggleButton(button: HTMLButtonElement, isEditMode: boolean) {
  if (!button) return
  
  if (isEditMode) {
    button.textContent = '👁️ View'
    button.classList.add('active')
    button.setAttribute('data-mode', 'edit')
  } else {
    button.textContent = '✏️ Edit'
    button.classList.remove('active')
    button.setAttribute('data-mode', 'view')
  }
}

function updateSaveButton(instanceId: string) {
  const state = milkdownInstances.get(instanceId)
  if (!state) return
  
  const container = document.querySelector(`[data-slug="${instanceId}"]`) as HTMLElement
  const saveBtn = container?.parentElement?.querySelector('.milkdown-save-btn') as HTMLButtonElement
  
  if (saveBtn) {
    if (state.isEditMode && state.hasChanges) {
      saveBtn.style.display = 'flex'
      saveBtn.classList.add('has-changes')
    } else {
      saveBtn.style.display = 'none'
      saveBtn.classList.remove('has-changes')
    }
  }
}

function markdownToHtml(markdown: string): string {
  // Simple markdown to HTML conversion
  // In a real implementation, you might want to use the same markdown processor as Quartz
  return markdown
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
    .replace(/\*(.*)\*/gim, '<em>$1</em>')
    .replace(/\n/gim, '<br>')
}

function showNotification(message: string, type: 'success' | 'error' | 'info' = 'info') {
  const notification = document.createElement('div')
  notification.className = `milkdown-notification milkdown-notification-${type}`
  notification.textContent = message
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
    color: white;
    border-radius: 4px;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  `
  
  document.body.appendChild(notification)
  
  setTimeout(() => {
    notification.remove()
  }, 3000)
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeMilkdown)

// Re-initialize when navigating in SPA mode
document.addEventListener('nav', initializeMilkdown)
