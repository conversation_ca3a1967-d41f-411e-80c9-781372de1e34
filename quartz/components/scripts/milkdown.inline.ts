import { Crepe } from "@milkdown/crepe"

interface MilkdownState {
  editor: Crepe | null
  isEditMode: boolean
  originalContent: string
  hasChanges: boolean
}

const milkdownInstances = new Map<string, MilkdownState>()

function initializeMilkdown() {
  const containers = document.querySelectorAll('.milkdown-editor-wrapper')
  
  containers.forEach((container) => {
    const editorElement = container.querySelector('.milkdown-editor') as HTMLElement
    const previewElement = container.querySelector('.milkdown-preview') as HTMLElement
    const toggleBtn = container.parentElement?.querySelector('.milkdown-toggle-btn') as HTMLButtonElement
    const saveBtn = container.parentElement?.querySelector('.milkdown-save-btn') as HTMLButtonElement
    
    if (!editorElement) return
    
    const content = container.getAttribute('data-content') || ''
    const filePath = container.getAttribute('data-file-path') || ''
    const slug = container.getAttribute('data-slug') || ''
    const defaultMode = container.getAttribute('data-default-mode') || 'view'
    const enableShortcuts = container.getAttribute('data-enable-shortcuts') === 'true'
    
    const instanceId = slug || filePath
    
    // Initialize state
    const state: MilkdownState = {
      editor: null,
      isEditMode: defaultMode === 'edit',
      originalContent: content,
      hasChanges: false
    }
    
    milkdownInstances.set(instanceId, state)
    
    // Set initial preview content
    if (previewElement && content) {
      previewElement.innerHTML = markdownToHtml(content)
    }
    
    // Initialize editor
    if (state.isEditMode) {
      initializeEditor(editorElement, content, instanceId)
    }
    
    // Set up toggle button
    if (toggleBtn) {
      toggleBtn.addEventListener('click', () => toggleEditMode(instanceId))
      updateToggleButton(toggleBtn, state.isEditMode)
    }
    
    // Set up save button
    if (saveBtn) {
      saveBtn.addEventListener('click', () => saveContent(instanceId))
    }
    
    // Set up keyboard shortcuts
    if (enableShortcuts) {
      setupKeyboardShortcuts(instanceId)
    }
    
    // Update UI based on initial mode
    updateUI(instanceId)
  })
}

function initializeEditor(element: HTMLElement, content: string, instanceId: string) {
  const state = milkdownInstances.get(instanceId)
  if (!state) return
  
  try {
    const editor = new Crepe({
      root: element,
      defaultValue: content,
    })
    
    // Configure the editor
    editor.editor.config(() => {
      // Add change listener
      const listener = () => {
        try {
          const currentContent = editor.getMarkdown()
          state.hasChanges = currentContent !== state.originalContent
          updateSaveButton(instanceId)
        } catch (error) {
          console.warn('Could not get markdown content:', error)
        }
      }

      // Set up the listener with a simple approach
      setTimeout(() => {
        const editorElement = element.querySelector('.milkdown') as HTMLElement
        if (editorElement) {
          editorElement.addEventListener('input', listener)
          editorElement.addEventListener('keyup', listener)
        }
      }, 100)
    })
    
    state.editor = editor
  } catch (error) {
    console.error('Failed to initialize Milkdown editor:', error)
    // Fallback to textarea
    element.innerHTML = `<textarea class="milkdown-fallback" style="width: 100%; min-height: 200px; padding: 1rem; border: none; resize: vertical; font-family: inherit;">${content}</textarea>`
  }
}

function toggleEditMode(instanceId: string) {
  const state = milkdownInstances.get(instanceId)
  if (!state) return
  
  const container = document.querySelector(`[data-slug="${instanceId}"]`) as HTMLElement
  if (!container) return
  
  const editorElement = container.querySelector('.milkdown-editor') as HTMLElement
  const previewElement = container.querySelector('.milkdown-preview') as HTMLElement
  const toggleBtn = container.parentElement?.querySelector('.milkdown-toggle-btn') as HTMLButtonElement
  
  state.isEditMode = !state.isEditMode
  
  if (state.isEditMode) {
    // Switch to edit mode
    if (!state.editor) {
      const content = state.originalContent
      initializeEditor(editorElement, content, instanceId)
    }
  } else {
    // Switch to view mode
    if (state.editor) {
      try {
        const currentContent = state.editor.getMarkdown()
        if (previewElement) {
          previewElement.innerHTML = markdownToHtml(currentContent)
        }
      } catch (error) {
        console.warn('Could not get markdown content for preview:', error)
      }
    }
  }
  
  updateToggleButton(toggleBtn, state.isEditMode)
  updateUI(instanceId)
}

function saveContent(instanceId: string) {
  const state = milkdownInstances.get(instanceId)
  if (!state || !state.editor) return

  let currentContent: string
  try {
    currentContent = state.editor.getMarkdown()
  } catch (error) {
    console.error('Could not get markdown content for saving:', error)
    return
  }
  
  // Here you would typically send the content to a server
  // For now, we'll just update the original content and show a message
  state.originalContent = currentContent
  state.hasChanges = false
  
  // Show save confirmation
  showNotification('Content saved successfully!', 'success')
  updateSaveButton(instanceId)
  
  // Update preview
  const container = document.querySelector(`[data-slug="${instanceId}"]`) as HTMLElement
  const previewElement = container?.querySelector('.milkdown-preview') as HTMLElement
  if (previewElement) {
    previewElement.innerHTML = markdownToHtml(currentContent)
  }
}

function setupKeyboardShortcuts(instanceId: string) {
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey || e.metaKey) {
      if (e.key === 'e') {
        e.preventDefault()
        toggleEditMode(instanceId)
      } else if (e.key === 's') {
        e.preventDefault()
        const state = milkdownInstances.get(instanceId)
        if (state?.isEditMode && state.hasChanges) {
          saveContent(instanceId)
        }
      }
    }
  })
}

function updateUI(instanceId: string) {
  const state = milkdownInstances.get(instanceId)
  if (!state) return
  
  const container = document.querySelector(`[data-slug="${instanceId}"]`) as HTMLElement
  if (!container) return
  
  const editorElement = container.querySelector('.milkdown-editor') as HTMLElement
  const previewElement = container.querySelector('.milkdown-preview') as HTMLElement
  
  if (state.isEditMode) {
    editorElement.style.display = 'block'
    if (previewElement) previewElement.style.display = 'none'
  } else {
    editorElement.style.display = 'none'
    if (previewElement) previewElement.style.display = 'block'
  }
}

function updateToggleButton(button: HTMLButtonElement, isEditMode: boolean) {
  if (!button) return
  
  if (isEditMode) {
    button.textContent = '👁️ View'
    button.classList.add('active')
    button.setAttribute('data-mode', 'edit')
  } else {
    button.textContent = '✏️ Edit'
    button.classList.remove('active')
    button.setAttribute('data-mode', 'view')
  }
}

function updateSaveButton(instanceId: string) {
  const state = milkdownInstances.get(instanceId)
  if (!state) return
  
  const container = document.querySelector(`[data-slug="${instanceId}"]`) as HTMLElement
  const saveBtn = container?.parentElement?.querySelector('.milkdown-save-btn') as HTMLButtonElement
  
  if (saveBtn) {
    if (state.isEditMode && state.hasChanges) {
      saveBtn.style.display = 'flex'
      saveBtn.classList.add('has-changes')
    } else {
      saveBtn.style.display = 'none'
      saveBtn.classList.remove('has-changes')
    }
  }
}

function markdownToHtml(markdown: string): string {
  // Simple markdown to HTML conversion
  // In a real implementation, you might want to use the same markdown processor as Quartz
  return markdown
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
    .replace(/\*(.*)\*/gim, '<em>$1</em>')
    .replace(/\n/gim, '<br>')
}

function showNotification(message: string, type: 'success' | 'error' | 'info' = 'info') {
  const notification = document.createElement('div')
  notification.className = `milkdown-notification milkdown-notification-${type}`
  notification.textContent = message
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
    color: white;
    border-radius: 4px;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  `
  
  document.body.appendChild(notification)
  
  setTimeout(() => {
    notification.remove()
  }, 3000)
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeMilkdown)

// Re-initialize when navigating in SPA mode
document.addEventListener('nav', initializeMilkdown)
