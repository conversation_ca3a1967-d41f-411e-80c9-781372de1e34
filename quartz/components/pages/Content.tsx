import { ComponentChildren } from "preact"
import { htmlToJsx } from "../../util/jsx"
import { QuartzComponent, QuartzComponentConstructor, QuartzComponentProps } from "../types"
// @ts-ignore
import milkdownScript from "../scripts/milkdown-content.inline"
import milkdownStyles from "../styles/milkdown.scss"

const Content: QuartzComponent = ({ fileData, tree }: QuartzComponentProps) => {
  const content = htmlToJsx(fileData.filePath!, tree) as ComponentChildren
  const classes: string[] = fileData.frontmatter?.cssclasses ?? []
  const classString = ["popover-hint", ...classes].join(" ")

  // Get the original markdown content
  const markdownContent = fileData.text || ""

  return (
    <div class="milkdown-content-wrapper">
      {/* Edit button */}
      <div class="milkdown-edit-controls">
        <button
          class="milkdown-edit-btn"
          data-slug={fileData.slug}
          title="Edit this content"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
            <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
          </svg>
          Edit
        </button>
        <button
          class="milkdown-save-btn"
          data-slug={fileData.slug}
          style="display: none;"
          title="Save changes"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
            <polyline points="17,21 17,13 7,13 7,21"/>
            <polyline points="7,3 7,8 15,8"/>
          </svg>
          Save
        </button>
      </div>

      {/* Content container that will be made editable */}
      <article
        class={`${classString} milkdown-editable-content`}
        data-slug={fileData.slug}
        data-file-path={fileData.filePath}
        data-markdown-content={markdownContent}
        data-editable="false"
      >
        {content}
      </article>
    </div>
  )
}

Content.css = milkdownStyles
Content.afterDOMLoaded = milkdownScript

export default (() => Content) satisfies QuartzComponentConstructor
