@use "sass:map";

@use "./variables.scss" as *;
@use "./syntax.scss";
@use "./callouts.scss";

html {
  scroll-behavior: smooth;
  text-size-adjust: none;
  overflow-x: hidden;
  width: 100vw;
}

body {
  margin: 0;
  box-sizing: border-box;
  background-color: var(--light);
  font-family: var(--bodyFont);
  color: var(--darkgray);
}

.text-highlight {
  background-color: var(--textHighlight);
  padding: 0 0.1rem;
  border-radius: 5px;
}
::selection {
  background: color-mix(in srgb, var(--tertiary) 60%, rgba(255, 255, 255, 0));
  color: var(--darkgray);
}

p,
ul,
text,
a,
tr,
td,
li,
ol,
ul,
.katex,
.math {
  color: var(--darkgray);
  fill: var(--darkgray);
  hyphens: auto;
}

p,
ul,
text,
a,
li,
ol,
ul,
.katex,
.math {
  overflow-wrap: anywhere;
  /* tr and td removed from list of selectors for overflow-wrap, allowing them to use default 'normal' property value */
}

.math {
  &.math-display {
    text-align: center;
  }
}

article {
  > mjx-container.MathJax,
  blockquote > div > mjx-container.MathJax {
    display: flex;
    > svg {
      margin-left: auto;
      margin-right: auto;
    }
  }
  blockquote > div > mjx-container.MathJax > svg {
    margin-top: 1rem;
    margin-bottom: 1rem;
  }
}

strong {
  font-weight: $semiBoldWeight;
}

a {
  font-weight: $semiBoldWeight;
  text-decoration: none;
  transition: color 0.2s ease;
  color: var(--secondary);

  &:hover {
    color: var(--tertiary);
  }

  &.internal {
    text-decoration: none;
    background-color: var(--highlight);
    padding: 0 0.1rem;
    border-radius: 5px;
    line-height: 1.4rem;

    &.broken {
      color: var(--secondary);
      opacity: 0.5;
      transition: opacity 0.2s ease;
      &:hover {
        opacity: 0.8;
      }
    }

    &:has(> img) {
      background-color: transparent;
      border-radius: 0;
      padding: 0;
    }
    &.tag-link {
      &::before {
        content: "#";
      }
    }
  }

  &.external .external-icon {
    height: 1ex;
    margin: 0 0.15em;

    > path {
      fill: var(--dark);
    }
  }
}

.flex-component {
  display: flex;
}

.desktop-only {
  display: initial;
  &.flex-component {
    display: flex;
  }
  @media all and ($mobile) {
    &.flex-component {
      display: none;
    }
    display: none;
  }
}

.mobile-only {
  display: none;
  &.flex-component {
    display: none;
  }
  @media all and ($mobile) {
    &.flex-component {
      display: flex;
    }
    display: initial;
  }
}

.page {
  max-width: calc(#{map.get($breakpoints, desktop)} + 300px);
  margin: 0 auto;
  & article {
    & > h1 {
      font-size: 2rem;
    }

    & li:has(> input[type="checkbox"]) {
      list-style-type: none;
      padding-left: 0;
    }

    & li:has(> input[type="checkbox"]:checked) {
      text-decoration: line-through;
      text-decoration-color: var(--gray);
      color: var(--gray);
    }

    & li > * {
      margin-top: 0;
      margin-bottom: 0;
    }

    p > strong {
      color: var(--dark);
    }
  }

  & > #quartz-body {
    display: grid;
    grid-template-columns: #{map.get($desktopGrid, templateColumns)};
    grid-template-rows: #{map.get($desktopGrid, templateRows)};
    column-gap: #{map.get($desktopGrid, columnGap)};
    row-gap: #{map.get($desktopGrid, rowGap)};
    grid-template-areas: #{map.get($desktopGrid, templateAreas)};

    @media all and ($tablet) {
      grid-template-columns: #{map.get($tabletGrid, templateColumns)};
      grid-template-rows: #{map.get($tabletGrid, templateRows)};
      column-gap: #{map.get($tabletGrid, columnGap)};
      row-gap: #{map.get($tabletGrid, rowGap)};
      grid-template-areas: #{map.get($tabletGrid, templateAreas)};
    }
    @media all and ($mobile) {
      grid-template-columns: #{map.get($mobileGrid, templateColumns)};
      grid-template-rows: #{map.get($mobileGrid, templateRows)};
      column-gap: #{map.get($mobileGrid, columnGap)};
      row-gap: #{map.get($mobileGrid, rowGap)};
      grid-template-areas: #{map.get($mobileGrid, templateAreas)};
    }

    @media all and not ($desktop) {
      padding: 0 1rem;
    }
    @media all and ($mobile) {
      margin: 0 auto;
    }

    & .sidebar {
      gap: 2rem;
      top: 0;
      box-sizing: border-box;
      padding: $topSpacing 2rem 2rem 2rem;
      display: flex;
      height: 100vh;
      position: sticky;
    }

    & .sidebar.left {
      z-index: 1;
      grid-area: grid-sidebar-left;
      flex-direction: column;
      @media all and ($mobile) {
        gap: 0;
        align-items: center;
        position: initial;
        display: flex;
        height: unset;
        flex-direction: row;
        padding: 0;
        padding-top: 2rem;
      }
    }

    & .sidebar.right {
      grid-area: grid-sidebar-right;
      margin-right: 0;
      flex-direction: column;
      @media all and ($mobile) {
        margin-left: inherit;
        margin-right: inherit;
      }
      @media all and not ($desktop) {
        position: initial;
        height: unset;
        width: 100%;
        flex-direction: row;
        padding: 0;
        & > * {
          flex: 1;
          max-height: 24rem;
        }
        & > .toc {
          display: none;
        }
      }
    }
    & .page-header,
    & .page-footer {
      margin-top: 1rem;
    }

    & .page-header {
      grid-area: grid-header;
      margin: $topSpacing 0 0 0;
      @media all and ($mobile) {
        margin-top: 0;
        padding: 0;
      }
    }

    & .center > article {
      grid-area: grid-center;
    }

    & footer {
      grid-area: grid-footer;
    }

    & .center,
    & footer {
      max-width: 100%;
      min-width: 100%;
      margin-left: auto;
      margin-right: auto;
      @media all and ($tablet) {
        margin-right: 0;
      }
      @media all and ($mobile) {
        margin-right: 0;
        margin-left: 0;
      }
    }
    & footer {
      margin-left: 0;
    }
  }
}

.footnotes {
  margin-top: 2rem;
  border-top: 1px solid var(--lightgray);
}

input[type="checkbox"] {
  transform: translateY(2px);
  color: var(--secondary);
  border: 1px solid var(--lightgray);
  border-radius: 3px;
  background-color: var(--light);
  position: relative;
  margin-inline-end: 0.2rem;
  margin-inline-start: -1.4rem;
  appearance: none;
  width: 16px;
  height: 16px;

  &:checked {
    border-color: var(--secondary);
    background-color: var(--secondary);

    &::after {
      content: "";
      position: absolute;
      left: 4px;
      top: 1px;
      width: 4px;
      height: 8px;
      display: block;
      border: solid var(--light);
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
    }
  }
}

blockquote {
  margin: 1rem 0;
  border-left: 3px solid var(--secondary);
  padding-left: 1rem;
  transition: border-color 0.2s ease;
}

h1,
h2,
h3,
h4,
h5,
h6,
thead {
  font-family: var(--headerFont);
  color: var(--dark);
  font-weight: revert;
  margin-bottom: 0;

  article > & > a[role="anchor"] {
    color: var(--dark);
    background-color: transparent;
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  &[id] > a[href^="#"] {
    margin: 0 0.5rem;
    opacity: 0;
    transition: opacity 0.2s ease;
    transform: translateY(-0.1rem);
    font-family: var(--codeFont);
    user-select: none;
  }

  &[id]:hover > a {
    opacity: 1;
  }

  &:not([id]) > a[role="anchor"] {
    display: none;
  }
}

// typography improvements
h1 {
  font-size: 1.75rem;
  margin-top: 2.25rem;
  margin-bottom: 1rem;
}

h2 {
  font-size: 1.4rem;
  margin-top: 1.9rem;
  margin-bottom: 1rem;
}

h3 {
  font-size: 1.12rem;
  margin-top: 1.62rem;
  margin-bottom: 1rem;
}

h4,
h5,
h6 {
  font-size: 1rem;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

figure[data-rehype-pretty-code-figure] {
  margin: 0;
  position: relative;
  line-height: 1.6rem;
  position: relative;

  & > [data-rehype-pretty-code-title] {
    font-family: var(--codeFont);
    font-size: 0.9rem;
    padding: 0.1rem 0.5rem;
    border: 1px solid var(--lightgray);
    width: fit-content;
    border-radius: 5px;
    margin-bottom: -0.5rem;
    color: var(--darkgray);
  }

  & > pre {
    padding: 0;
  }
}

pre {
  font-family: var(--codeFont);
  padding: 0 0.5rem;
  border-radius: 5px;
  overflow-x: auto;
  border: 1px solid var(--lightgray);
  position: relative;

  &:has(> code.mermaid) {
    border: none;
  }

  & > code {
    background: none;
    padding: 0;
    font-size: 0.85rem;
    counter-reset: line;
    counter-increment: line 0;
    display: grid;
    padding: 0.5rem 0;
    overflow-x: auto;

    & [data-highlighted-chars] {
      background-color: var(--highlight);
      border-radius: 5px;
    }

    & > [data-line] {
      padding: 0 0.25rem;
      box-sizing: border-box;
      border-left: 3px solid transparent;

      &[data-highlighted-line] {
        background-color: var(--highlight);
        border-left: 3px solid var(--secondary);
      }

      &::before {
        content: counter(line);
        counter-increment: line;
        width: 1rem;
        margin-right: 1rem;
        display: inline-block;
        text-align: right;
        color: rgba(115, 138, 148, 0.6);
      }
    }

    &[data-line-numbers-max-digits="2"] > [data-line]::before {
      width: 2rem;
    }

    &[data-line-numbers-max-digits="3"] > [data-line]::before {
      width: 3rem;
    }
  }
}

code {
  font-size: 0.9em;
  color: var(--dark);
  font-family: var(--codeFont);
  border-radius: 5px;
  padding: 0.1rem 0.2rem;
  background: var(--lightgray);
}

tbody,
li,
p {
  line-height: 1.6rem;
}

.table-container {
  overflow-x: auto;

  & > table {
    margin: 1rem;
    padding: 1.5rem;
    border-collapse: collapse;

    th,
    td {
      min-width: 75px;
    }

    & > * {
      line-height: 2rem;
    }
  }
}

th {
  text-align: left;
  padding: 0.4rem 0.7rem;
  border-bottom: 2px solid var(--gray);
}

td {
  padding: 0.2rem 0.7rem;
}

tr {
  border-bottom: 1px solid var(--lightgray);
  &:last-child {
    border-bottom: none;
  }
}

img {
  max-width: 100%;
  border-radius: 5px;
  margin: 1rem 0;
  content-visibility: auto;
}

p > img + em {
  display: block;
  transform: translateY(-1rem);
}

hr {
  width: 100%;
  margin: 2rem auto;
  height: 1px;
  border: none;
  background-color: var(--lightgray);
}

audio,
video {
  width: 100%;
  border-radius: 5px;
}

.spacer {
  flex: 2 1 auto;
}

div:has(> .overflow) {
  max-height: 100%;
  overflow-y: hidden;
}

ul.overflow,
ol.overflow {
  max-height: 100%;
  overflow-y: auto;
  width: 100%;
  margin-bottom: 0;

  // clearfix
  content: "";
  clear: both;

  & > li.overflow-end {
    height: 0.5rem;
    margin: 0;
  }

  &.gradient-active {
    mask-image: linear-gradient(to bottom, black calc(100% - 50px), transparent 100%);
  }
}

.transclude {
  ul {
    padding-left: 1rem;
  }
}

.katex-display {
  display: initial;
  overflow-x: auto;
  overflow-y: hidden;
}

.external-embed.youtube,
iframe.pdf {
  aspect-ratio: 16 / 9;
  height: 100%;
  width: 100%;
  border-radius: 5px;
}

.navigation-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 0;
  height: 3px;
  background: var(--secondary);
  transition: width 0.2s ease;
  z-index: 9999;
}
