@use "sass:map";

/**
 * Layout breakpoints
 * $mobile: screen width below this value will use mobile styles
 * $desktop: screen width above this value will use desktop styles
 * Screen width between $mobile and $desktop width will use the tablet layout.
 * assuming mobile < desktop
 */
$breakpoints: (
  mobile: 800px,
  desktop: 1200px,
);

$mobile: "(max-width: #{map.get($breakpoints, mobile)})";
$tablet: "(min-width: #{map.get($breakpoints, mobile)}) and (max-width: #{map.get($breakpoints, desktop)})";
$desktop: "(min-width: #{map.get($breakpoints, desktop)})";

$pageWidth: #{map.get($breakpoints, mobile)};
$sidePanelWidth: 320px; //380px;
$topSpacing: 6rem;
$boldWeight: 700;
$semiBoldWeight: 600;
$normalWeight: 400;

$mobileGrid: (
  templateRows: "auto auto auto auto auto",
  templateColumns: "auto",
  rowGap: "5px",
  columnGap: "5px",
  templateAreas:
    '"grid-sidebar-left"\
      "grid-header"\
      "grid-center"\
      "grid-sidebar-right"\
      "grid-footer"',
);
$tabletGrid: (
  templateRows: "auto auto auto auto",
  templateColumns: "#{$sidePanelWidth} auto",
  rowGap: "5px",
  columnGap: "5px",
  templateAreas:
    '"grid-sidebar-left grid-header"\
      "grid-sidebar-left grid-center"\
      "grid-sidebar-left grid-sidebar-right"\
      "grid-sidebar-left grid-footer"',
);
$desktopGrid: (
  templateRows: "auto auto auto",
  templateColumns: "#{$sidePanelWidth} auto #{$sidePanelWidth}",
  rowGap: "5px",
  columnGap: "5px",
  templateAreas:
    '"grid-sidebar-left grid-header grid-sidebar-right"\
      "grid-sidebar-left grid-center grid-sidebar-right"\
      "grid-sidebar-left grid-footer grid-sidebar-right"',
);
