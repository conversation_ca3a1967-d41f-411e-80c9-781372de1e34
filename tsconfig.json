{"compilerOptions": {"lib": ["esnext", "DOM", "DOM.Iterable"], "experimentalDecorators": true, "module": "esnext", "target": "esnext", "moduleResolution": "node", "strict": true, "incremental": true, "resolveJsonModule": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "noUnusedLocals": true, "noUnusedParameters": true, "esModuleInterop": true, "jsx": "react-jsx", "jsxImportSource": "preact"}, "include": ["**/*.ts", "**/*.tsx", "./package.json"], "exclude": ["build/**/*.d.ts"]}